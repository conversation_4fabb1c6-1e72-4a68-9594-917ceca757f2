<template>
  <div class="device-operation">
    <div class="device-list">
      <!-- 无设备时的显示 -->
      <div v-if="deviceList.length === 0" class="no-device">
        <span class="no-device-text">无设备</span>
      </div>

      <!-- 动态渲染设备列表 -->
      <div
        v-for="(device, index) in deviceList"
        :key="`device-${device.equipmentId || device.equipmentSn || device.item || index}-${index}`"
        class="device-item"
        :class="[
          device.hasOpenScale ? 'slider-item' : 'switch-item',
          { 'device-disabled': !isDeviceOnline(device) }
        ]"
      >
        <div class="device-header">
          <span class="device-name">{{ getDeviceDisplayName(device) }}</span>

          <!-- 开关控制 -->
          <div v-if="!device.hasOpenScale" class="switch-container">
            <el-switch
              v-model="device.value"
              active-color="#00D4FF"
              inactive-color="rgba(255,255,255,0.3)"
              :disabled="!isDeviceOnline(device)"
              @change="(value) => onSwitchChange(value, device, index)"
            ></el-switch>
          </div>

          <span class="device-status" :class="getDeviceStatusClass(device)">
            {{ getDeviceStatusText(device) }}
          </span>
        </div>

        <!-- 滑块控制 -->
        <div v-if="device.hasOpenScale" class="device-control">
          <div class="slider-container">
            <div class="custom-slider-marks">
              <div class="mark-line" style="left: 0.5%"></div>
              <div class="mark-line" style="left: 50%"></div>
              <div class="mark-line" style="left: 99.8%"></div>
            </div>
            <el-slider
              v-model="device.value"
              :min="0"
              :max="100"
              :show-tooltip="true"
              :format-tooltip="formatTooltip"
              :marks="{ 0: '0%', 50: '50%', 100: '100%' }"
              :disabled="!isDeviceOnline(device)"
              @change="(value) => onSliderChange(value, device, index)"
            ></el-slider>
          </div>
        </div>
      </div>
    </div>

    <!-- 二次确认提示框 -->
    <div class="confirm-dialog-wrapper">
      <el-dialog
        title="提示"
        :visible.sync="confirmDialog.visible"
        width="374px"
        :show-close="false"
        :close-on-click-modal="true"
        :close-on-press-escape="false"
        :modal-append-to-body="false"
        center
        @close="cancelConfirm"
      >
        <img src="../assets/image/centralControlPlatform/dialogLine.png" alt="" class="dialog-line">
        <div class="dialog-content">
          <p>{{ confirmDialog.message }}</p>
        </div>
        <div slot="footer" class="dialog-footer">
          <div class="searchButtonStyle2">
            <el-button @click="cancelConfirm">取消</el-button>
          </div>
          <div class="submitButtonStyle2">
            <el-button @click="confirmChange">确定</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import EquipmentCommunicationService from '../jaxrs/concrete/com.zny.ia.api.ConcreteService.EquipmentCommunicationService.js'
import 控制类指标项 from '../jaxrs/constants/com.zny.ia.constant.deviceDef.控制类指标项.js'
export default {
  name: 'DeviceOperation',
  props: {
    deviceData: {
      type: [Array, Object],
      default: () => []
    }
  },
  data() {
    return {
      deviceList: [], // 动态设备列表，初始为空
      confirmDialog: {
        visible: false,
        message: '',
        deviceIndex: -1,
        newValue: 0,
        oldValue: 0
      }
    }
  },
  watch: {
    // 监听props变化，更新设备数据
    deviceData: {
      handler(newData) {
        if (newData) {
          console.log('接收到设备操作数据:', newData);
          this.updateDeviceData(newData);
        } else {
          // 如果没有数据，清空设备列表
          this.deviceList = [];
        }
      },
      immediate: true,
      deep: true
    },
    // 监听确认对话框的显示状态
    'confirmDialog.visible': {
      handler(newVisible, oldVisible) {
        if (newVisible) {
          // 对话框显示时，立即隐藏所有tooltip
          this.hideAllSliderTooltips()
        } else if (oldVisible) {
          // 对话框关闭时，延迟隐藏tooltip确保状态同步
          this.$nextTick(() => {
            setTimeout(() => {
              this.hideAllSliderTooltips()
            }, 100)
          })
        }
      }
    }
  },
  methods: {
    formatTooltip(value) {
      return `${value}%`
    },

    onSliderChange(value, device, index) {
      // 检查设备是否在线
      if (!this.isDeviceOnline(device)) {
        this.$message.warning('设备离线，无法操作');
        // 恢复到原始值
        device.value = device.originalValue;
        return;
      }

      // 隐藏所有滑块的tooltip
      this.hideAllSliderTooltips()

      this.confirmDialog = {
        visible: true,
        message: `是否将${this.getDeviceDisplayName(device)}调到${value}%`,
        deviceIndex: index,
        newValue: value,
        oldValue: device.originalValue
      }
    },
    async confirmChange() {
      const device = this.deviceList[this.confirmDialog.deviceIndex]
      if (!device) {
        this.confirmDialog.visible = false
        return
      }

      try {
        // 构造设备控制参数
        const controlParam = this.buildControlParam(device, this.confirmDialog.newValue);
        const equipmentCtrlVO = {
          equipmentId: device.equipmentId,
          item: 控制类指标项[device.item],
          param: controlParam
        }

        console.log('设备控制参数构造:', {
          deviceSeq: device.seq || '默认01',
          deviceType: device.hasOpenScale ? '开度控制' : '开关控制',
          originalValue: this.confirmDialog.newValue,
          constructedParam: controlParam,
          paramFormat: device.hasOpenScale ? '序号(2位) + E + 开度(3位)' : '序号(2位) + A + 动作(3位)'
        });
        console.log('发送设备控制指令:', equipmentCtrlVO)

        // 调用设备控制接口，返回订阅号
        const response = await EquipmentCommunicationService.equipmentCtrl(equipmentCtrlVO)
        console.log('设备控制接口响应:', response)

        if (response && response.code === 200 && response.data) {
          const subscriptionId = response.data
          console.log('获得订阅号:', subscriptionId)

          // 更新本地状态
          device.value = this.confirmDialog.newValue
          device.originalValue = this.confirmDialog.newValue

          // 直接查询指令执行结果
          this.checkCommandResult(subscriptionId, device)
        } else {
          // 控制失败，恢复原状态
          device.value = device.originalValue
          this.$message.error('设备控制指令发送失败')
        }
      } catch (error) {
        console.error('设备控制异常:', error)
        // 控制失败，恢复原状态
        device.value = device.originalValue
        this.$message.error('设备控制失败')
      }

      this.confirmDialog.visible = false

      // 确保tooltip被隐藏
      this.$nextTick(() => {
        this.hideAllSliderTooltips()
      })
    },
    cancelConfirm() {
      // 取消修改，恢复到原始值
      const device = this.deviceList[this.confirmDialog.deviceIndex]
      if (device) {
        device.value = device.originalValue
      }
      this.confirmDialog.visible = false

      // 确保tooltip被隐藏
      this.$nextTick(() => {
        this.hideAllSliderTooltips()
      })
    },
    onSwitchChange(value, device, index) {
      // 检查设备是否在线
      if (!this.isDeviceOnline(device)) {
        this.$message.warning('设备离线，无法操作');
        // 恢复到原始值
        device.value = device.originalValue;
        return;
      }

      this.confirmDialog = {
        visible: true,
        message: `是否将${this.getDeviceDisplayName(device)}${value ? '打开' : '关闭'}`,
        deviceIndex: index,
        newValue: value,
        oldValue: device.originalValue
      }
    },

    // 根据订阅号查询指令执行结果
    async checkCommandResult(subscriptionId, device) {
      try {
        console.log(`查询指令执行结果，订阅号: ${subscriptionId}`)
        
        const response = await EquipmentCommunicationService.gainCMDResult(subscriptionId)
        console.log('指令执行结果响应:', response)

        if (response && response.code === 200) {
          this.$message.success(`${this.getDeviceDisplayName(device)}控制成功`)
          console.log('设备控制成功:', response.data)
        } else {
          this.$message.error(`${this.getDeviceDisplayName(device)}控制失败`)
          console.error('设备控制失败:', response)
        }
      } catch (error) {
        console.error('查询指令执行结果异常:', error)
        this.$message.error(`${this.getDeviceDisplayName(device)}控制失败`)
      }
    },

    // 隐藏所有滑块的tooltip
    hideAllSliderTooltips() {
      // 通过DOM操作隐藏tooltip
      this.$nextTick(() => {
        const sliders = this.$el.querySelectorAll('.el-slider')
        sliders.forEach(slider => {
          const sliderButton = slider.querySelector('.el-slider__button')
          if (sliderButton) {
            // 创建并触发鼠标离开事件
            const mouseLeaveEvent = new MouseEvent('mouseleave', {
              bubbles: true,
              cancelable: true,
              view: window
            })
            sliderButton.dispatchEvent(mouseLeaveEvent)

            // 移除focus状态
            sliderButton.blur()

            // 额外确保tooltip被隐藏
            const tooltipInstance = sliderButton._tooltip || sliderButton.__tooltip
            if (tooltipInstance && tooltipInstance.hide) {
              tooltipInstance.hide()
            }
          }
        })
      })
    },

    // 更新设备数据
    updateDeviceData(deviceData) {
      // 处理数组类型的数据
      if (Array.isArray(deviceData)) {
        console.log('处理设备控制项数据(数组):', deviceData);

        // 转换数据结构，渲染所有设备（包括离线设备）
        this.deviceList = deviceData.map(equipment => {
          const hasOpenScale = equipment.openScale !== undefined && equipment.openScale !== null;

          let value, originalValue;
          if (hasOpenScale) {
            // 有openScale的是滑块类型
            value = equipment.openScale || 0;
            originalValue = equipment.openScale || 0;
          } else {
            // 没有openScale的是开关类型，默认关闭
            value = false;
            originalValue = false;
          }

          return {
            equipmentId: equipment.equipmentId,
            equipmentSn: equipment.equipmentSn,
            item: equipment.item, // 直接使用返回的设备名称
            status: equipment.status, // 状态字符串
            hasOpenScale: hasOpenScale, // 标识是否有开度控制
            value: value,
            originalValue: originalValue,
            openScale: equipment.openScale,
            seq: equipment.seq
          };
        });

        console.log('转换后的设备列表:', this.deviceList);
      } else if (deviceData && typeof deviceData === 'object') {
        // 处理对象类型的数据（兼容旧格式或其他格式）
        console.log('处理设备控制项数据(对象):', deviceData);

        // 如果是对象，尝试清空设备列表或根据对象结构处理
        this.deviceList = [];
      } else {
        // 其他情况，清空设备列表
        this.deviceList = [];
      }
    },

    // 检查设备是否在线
    isDeviceOnline(device) {
      // 根据您的要求，状态为'1'是离线，其他都是在线
      return device && device.status !== '1';
    },

    // 获取设备状态文本
    getDeviceStatusText(device) {
      if (!device) return '未知';
      return device.status === '1' ? '离线' : '在线';
    },

    // 获取设备状态样式类
    getDeviceStatusClass(device) {
      if (!device) return '';
      return device.status === '1' ? 'offline' : 'online';
    },

    // 获取设备显示名称（控制项 + 序号）
    getDeviceDisplayName(device) {
      if (!device) return '';

      // 如果有序号，则显示为 "控制项(序号)"
      if (device.seq !== undefined && device.seq !== null && device.seq !== '') {
        return `${device.item}${device.seq}`;
      }

      // 最后兜底，直接返回控制项名称
      return device.item || '';
    },

    // 构造设备控制参数
    buildControlParam(device, value) {
      if (!device) return '';

      // 获取设备序号，默认为01
      let deviceSeq = '01';
      if (device.seq !== undefined && device.seq !== null && device.seq !== '') {
        // 确保序号是2位数字格式
        deviceSeq = device.seq.toString().padStart(2, '0');
      }

      if (device.hasOpenScale) {
        // 有开度控制的设备：序号 + E + 3位数字（开度百分比）
        // 确保值在0-100范围内
        const openScale = Math.max(0, Math.min(100, parseInt(value) || 0));
        // 转换为3位数字格式，不足3位前面补0
        const paddedValue = openScale.toString().padStart(3, '0');
        return `${deviceSeq}E${paddedValue}`;
      } else {
        // 开关类型设备：序号 + A + 3位数字
        // true/1 -> A001 (打开), false/0 -> A002 (关闭)
        const action = value ? '001' : '002';
        return `${deviceSeq}A${action}`;
      }
    }
  }
}
</script>

<style lang="less" scoped>
.device-operation {
  width: 100%;
  height: 100%;
  padding: 15px 0;

  .device-list {
    width: 100%;
    height: 90%;
    display: flex;
    overflow-y: auto;
    // justify-content: center;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;

    .no-device {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .no-device-text {
        color: #FFFFFF;
        font-size: 16px;
        font-family: MicrosoftYaHei;
        opacity: 0.6;
      }
    }

    .device-item {
      width: 358px;
      background: #076F7D;
      // border: 1px solid #00D4FF;
      // border-radius: 4px;
      padding: 11px 12px;
      margin-bottom: 12px;
      position: relative;

      &:last-child {
        margin-bottom: 0;
      }

      // 设备禁用状态样式
      &.device-disabled {
        opacity: 0.6;
        pointer-events: none;

        .device-name {
          color: rgba(255, 255, 255, 0.5) !important;
        }

        .custom-slider-marks .mark-line {
          background: rgba(255, 255, 255, 0.3) !important;
        }
      }

      &.slider-item {
        height: 96px;
        box-sizing: border-box;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 10.8px;
          border-left: 1px solid #3CF1DD;
          border-right: 1px solid #3CF1DD;
          border-bottom: 1px solid #3CF1DD;
          background: transparent;
        }
      }

      &.switch-item {
        height: 48px;
        box-sizing: border-box;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 10.8px;
          border-left: 1px solid #3CF1DD;
          border-right: 1px solid #3CF1DD;
          border-bottom: 1px solid #3CF1DD;
          background: transparent;
        }
      }

      .device-header {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        height: 20px;

        .device-name {
          color: #FFFFFF;
          font-size: 16px;
          font-family: MicrosoftYaHei;
          margin-right: auto;
        }

        .device-value {
          color: #FFFFFF;
          font-size: 16px;
          font-family: MicrosoftYaHei;
          margin-right: 15px;
          background: rgba(0, 0, 0, 0.3);
          padding: 2px 8px;
          border-radius: 3px;
        }

        .device-status {
          font-size: 16px;
          font-family: MicrosoftYaHei;

          &.online {
            color: #18FF49;
          }

          &.offline {
            color: #DADCDF;
          }
        }
      }

      .device-control {
        .slider-container {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;

          // 自定义刻度线
          .custom-slider-marks {
            position: absolute;
            top: 21px;
            width: 277px;
            height: 7px;
            z-index: 1;

            .mark-line {
              position: absolute;
              width: 1px;
              height: 7px;
              background: #FFFFFF;
              transform: translateX(-50%);
            }
          }

          // el-slider 自定义样式
          :deep(.el-slider) {
            width: 277px;

            .el-slider__runway {
              height: 4px;
              background: rgba(255,255,255,0.3);
              border-radius: 2px;
            }

            .el-slider__bar {
              background: #3CF1DD;
              border-radius: 2px;
            }

            .el-slider__button {
              width: 14px;
              height: 14px;
              background: #FFFFFF;
              border: none;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            // 禁用状态样式
            &.is-disabled {
              .el-slider__runway {
                background: rgba(255,255,255,0.1) !important;
              }

              .el-slider__bar {
                background: rgba(60, 241, 221, 0.3) !important;
              }

              .el-slider__button {
                background: rgba(255, 255, 255, 0.5) !important;
                cursor: not-allowed !important;
              }
            }

            // 隐藏默认的刻度点
            .el-slider__stop {
              display: none;
            }

            .el-slider__marks {
              top: 12px;

              .el-slider__marks-text {
                color: #FFFFFF;
                font-size: 12px;
                font-family: MicrosoftYaHei;
              }
            }

            .el-tooltip__popper {
              background: #000000 !important;
              color: #FFFFFF !important;
              font-size: 12px !important;

              .el-tooltip__arrow {
                border-top-color: #000000 !important;
              }
            }
          }


        }
      }

      // 水泵特殊样式
      &.switch-item {
        .device-header {
          margin-bottom: 0;
          height: 24px;
          align-items: center;

          .switch-container {
            margin-left: auto;
            margin-right: 15px;

            // el-switch 自定义样式
            :deep(.el-switch) {
              width: 46px;
              height: 22px;

              .el-switch__core {
                width: 46px;
                height: 22px;
                border-radius: 11px;
                border: none;
                background-color: rgba(255, 255, 255, 0.3);

                &::after {
                  width: 18px;
                  height: 18px;
                  top: 2px;
                  left: 2px;
                  background-color: #FFFFFF;
                  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
                }
              }

              &.is-checked .el-switch__core {
                background-color: #3CF1DD!important;

                &::after {
                  left: 35px;
                }
              }

              // 禁用状态样式
              &.is-disabled {
                opacity: 0.6;
                cursor: not-allowed;

                .el-switch__core {
                  background-color: rgba(255, 255, 255, 0.1) !important;

                  &::after {
                    background-color: rgba(255, 255, 255, 0.5) !important;
                  }
                }

                &.is-checked .el-switch__core {
                  background-color: rgba(60, 241, 221, 0.3) !important;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 确认对话框样式
.confirm-dialog-wrapper {
  :deep(.v-modal) {
    background: rgba(0, 0, 0, 0.5) !important;
  }

  :deep(.el-dialog) {
    width: 374px !important;
    height: 236px;
    background: #003D42;
    // border-radius: 8px;
    font-family: MicrosoftYaHei;
    margin-top: calc(50vh - 5px) !important;

    .el-dialog__header {
      height: 60px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-dialog__title {
        color: #DFEEF3;
        font-size: 16px;
        font-weight: bold;
      }
    }

    .el-dialog__body {
      padding: 0;
      height: calc(100% - 60px - 80px);
      display: flex;
      flex-direction: column;
      color: #DFEEF3;
      .dialog-line {
        position: relative;
        top: -20px;
      }

      .dialog-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;

        p {
          color: #DFEEF3;
          font-size: 16px;
          margin: 0;
          text-align: center;
          line-height: 1.5;
        }
      }
    }

    .el-dialog__footer {
      height: 3px;
      padding: 0 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 40px;

      .dialog-footer {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 40px;

        .searchButtonStyle2,
        .submitButtonStyle2 {
          width: 120px;
          height: 40px;
        }
      }
    }
  }
}
</style>

<style lang="less">
@import '../assets/css/app.less';
</style>
